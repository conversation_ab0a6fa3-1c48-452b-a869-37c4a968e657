import {
  createLocalAudioTrack,
  createLocalTracks,
  createLocalVideoTrack,
  facingModeFromLocalTrack,
  Track,
  VideoPresets,
  Mutex,
} from "livekit-client";
import * as React from "react";


import {
  UserOutlined,
  AudioOutlined,
  AudioMutedOutlined,
  SettingOutlined,
  CaretDownFilled,
} from "@ant-design/icons";

import {
  // MediaDeviceMenu,
  // TrackToggle,
  useMediaDevices,
  usePersistentUserChoices,
} from "@livekit/components-react";
import { generateAvatar } from "../utils/helper";
import "../styles/Prejoin.scss";
import "../styles/audioVideoPrejoin.scss";
import "../styles/videoHeightControl.scss";
// import AudioVideoSettingsModal from "../components/AudioVideoSettingsModal/AudioVideoSettingsModal";
// import VirtualBackgroundModal from "../components/AudioVideoSettingsModal/VirtualBackgroundModal";
// import AudioSettings from "../components/AudioVideoSettingsModal/AudioSettings";
// import VideoSettings from "../components/AudioVideoSettingsModal/VideoSettings";
import { ReactComponent as VirtualBackgroundIcon } from "./icons/Virtualbackground.svg";
import { ReactComponent as WarningIcon } from "./icons/waringIcon.svg";
import VirtualBackgroundModal from "../components/AudioVideoSettingsModal/VirtualBackgroundModal";
import PermissionUi from '../components/permissionUi/permissionUi';
import { ReactComponent as VideoOff } from "./icons/VideoOff.svg";
import { ReactComponent as VideoOn } from "./icons/VideoOn.svg";
import MicDeviceDropdown from "../components/AudioVideoSettingsModal/MicDeviceDropdown";
import CameraDeviceDropdown from "../components/AudioVideoSettingsModal/CameraDeviceDropdown";

// Add mobile browser check function
const isMobileBrowser = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

/**
 * @public
 */
export function usePreviewTracks(options, onError, setToastMessage, setToastStatus, setShowToast) {
  const [tracks, setTracks] = React.useState();
  const trackLock = React.useMemo(() => new Mutex(), []);

  React.useEffect(() => {
    let needsCleanup = false;
    let localTracks = [];
    trackLock.lock().then(async (unlock) => {
      try {
        if (options.audio || options.video) {
          localTracks = await createLocalTracks(options);

          if (needsCleanup) {
            localTracks.forEach((tr) => tr.stop());
          } else {
            setTracks(localTracks);
          }
        }
      } catch (e) {
        if (onError && e instanceof Error) {
          onError(e);
        } else {
          console.error("Error creating local tracks:", e);
          setToastMessage("Permissions denied for accessing audio/video devices. Please check your browser settings.");
          setToastStatus("error");
          setShowToast(true);
        }
      } finally {
        unlock();
      }
    });

    return () => {
      needsCleanup = true;
      localTracks.forEach((track) => {
        track.stop();
      });
    };
  }, [JSON.stringify(options), onError, trackLock]);

  return tracks;
}

/** @public */
export function usePreviewDevice(enabled, deviceId, deviceKind) {
  const [deviceError, setDeviceError] = React.useState(null);
  const [isCreatingTrack, setIsCreatingTrack] = React.useState(false);

  const devices = useMediaDevices({ kind: deviceKind });
  const [selectedDevice, setSelectedDevice] = React.useState(undefined);

  const [localTrack, setLocalTrack] = React.useState();
  const [localDeviceId, setLocalDeviceId] = React.useState(deviceId);

  React.useEffect(() => {
    setLocalDeviceId(deviceId);
  }, [deviceId]);

  const prevDeviceId = React.useRef(localDeviceId); // Moved prevDeviceId declaration here

  const createTrack = async (id, kind) => {
    try {
      const track =
        kind === "videoinput"
          ? await createLocalVideoTrack({
              deviceId: id,
              resolution: VideoPresets.h720.resolution,
            })
          : await createLocalAudioTrack({ deviceId: id });

      const newDeviceId = await track.getDeviceId();
      if (newDeviceId && id !== newDeviceId) {
        prevDeviceId.current = newDeviceId;
        setLocalDeviceId(newDeviceId);
      }
      setLocalTrack(track);
    } catch (e) {
      if (e instanceof Error) {
        setDeviceError(e);
      }
    }
  };

  const switchDevice = async (track, id) => {
    await track.setDeviceId(id);
    prevDeviceId.current = id;
  };

  React.useEffect(() => {
    if (enabled && !localTrack && !deviceError && !isCreatingTrack) {
      setIsCreatingTrack(true);
      createTrack(localDeviceId, deviceKind).finally(() => {
        setIsCreatingTrack(false);
      });
    }
  }, [
    enabled,
    localTrack,
    deviceError,
    isCreatingTrack,
    localDeviceId,
    deviceKind,
  ]);

  React.useEffect(() => {
    if (!localTrack) {
      return;
    }
    if (!enabled) {
      localTrack.mute().then(() => console.log(localTrack.mediaStreamTrack));
    } else if (
      selectedDevice?.deviceId &&
      prevDeviceId.current !== selectedDevice?.deviceId
    ) {
      switchDevice(localTrack, selectedDevice.deviceId);
    } else {
      localTrack.unmute();
    }
  }, [localTrack, selectedDevice, enabled, deviceKind]);

  React.useEffect(() => {
    return () => {
      if (localTrack) {
        localTrack.stop();
        localTrack.mute();
      }
    };
  }, [localTrack]);

  React.useEffect(() => {
    setSelectedDevice(devices?.find((dev) => dev.deviceId === localDeviceId));
  }, [localDeviceId, devices]);

  return {
    selectedDevice,
    localTrack,
    deviceError,
  };
}

export function PreJoinAudioVideo({
  defaults = {},
  onValidate,
  onError,
  username,
  setIsValid,
  setUserChoices,
  persistUserChoices = true,
  setToastMessage,
  setToastStatus,
  setShowToast,
  setToastNotification,
  room,
  backgrounds,
  setBackgrounds,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  setDeviceIdAudio,
  ...htmlProps
}) {
  const [isVideoModalOpen, setIsVideoModalOpen] = React.useState(false);
  const [isVisualEffectsModalOpen, setIsVisualEffectsModalOpen] = React.useState(false);
  const [showPermissionModal, setShowPermissionModal] = React.useState(false);

  // Commented out for future use
  // const [noiseCancellation, setNoiseCancellation] = React.useState(
  //   room?.options?.audioCaptureDefaults?.noiseSuppression
  // );
  // const [echoCancellation, setEchoCancellation] = React.useState(
  //   room?.options?.audioCaptureDefaults?.echoCancellation
  // );

  // Simplified permission state - start with 'unknown' to avoid premature warnings
  const [permissions, setPermissions] = React.useState({
    camera: 'unknown', // 'unknown', 'granted', 'denied'
    microphone: 'unknown'
  });

  // Add state for permission type
  const [permissionType, setPermissionType] = React.useState('both');

  // Feature detection for permission API
  const hasPermissionAPI = React.useMemo(() => 'permissions' in navigator, []);

  // Browser detection for Safari/iOS
  const isSafari = React.useMemo(() => /^((?!chrome|android).)*safari/i.test(navigator.userAgent), []);

  const partialDefaults = {
    ...(defaults.audioDeviceId !== undefined && {
      audioDeviceId: defaults.audioDeviceId,
    }),
    ...(defaults.videoDeviceId !== undefined && {
      videoDeviceId: defaults.videoDeviceId,
    }),
    audioEnabled: false,
    videoEnabled: false,
    ...(defaults.username !== undefined && { username: defaults.username }),
  };

  const {
    userChoices: initialUserChoices,
    saveAudioInputDeviceId,
    saveAudioInputEnabled,
    saveVideoInputDeviceId,
    saveVideoInputEnabled,
    saveUsername,
  } = usePersistentUserChoices({
    defaults: partialDefaults,
    preventSave: !persistUserChoices,
    preventLoad: !persistUserChoices,
  });

  const [audioEnabled, setAudioEnabled] = React.useState(false);
  const [videoEnabled, setVideoEnabled] = React.useState(false);
  const [audioDeviceId, setAudioDeviceId] = React.useState(
    initialUserChoices.audioDeviceId
  );
  const [videoDeviceId, setVideoDeviceId] = React.useState(
    initialUserChoices.videoDeviceId
  );
  // const [speakerDeviceId, setSpeakerDeviceId] = React.useState("");
  const [avatarName, setAvatarName] = React.useState("");

  React.useEffect(() => {
    saveAudioInputEnabled(audioEnabled);
  }, [audioEnabled, saveAudioInputEnabled]);
  React.useEffect(() => {
    saveVideoInputEnabled(videoEnabled);
  }, [videoEnabled, saveVideoInputEnabled]);
  React.useEffect(() => {
    saveAudioInputDeviceId(audioDeviceId);
  }, [audioDeviceId, saveAudioInputDeviceId]);
  React.useEffect(() => {
    saveVideoInputDeviceId(videoDeviceId);
  }, [videoDeviceId, saveVideoInputDeviceId]);
  React.useEffect(() => {
    saveUsername(username);
  }, [username, saveUsername]);

  React.useEffect(() => {
    if (username) {
      setAvatarName(generateAvatar(username));
    } else {
      setAvatarName(null);
    }
  }, [username]);

  const tracks = usePreviewTracks(
    {
      audio: audioEnabled
        ? { deviceId: initialUserChoices.audioDeviceId }
        : false,
      video: videoEnabled
        ? { deviceId: initialUserChoices.videoDeviceId }
        : false,
    },
    onError,
    setToastMessage,
    setToastStatus,
    setShowToast
  );

  const videoEl = React.useRef(null);

  const videoTrack = React.useMemo(
    () => tracks?.filter((track) => track.kind === Track.Kind.Video)[0],
    [tracks]
  );

  const trackFacingMode = React.useMemo(() => {
    if (videoTrack) {
      const { facingMode } = facingModeFromLocalTrack(videoTrack);
      return facingMode;
    } else {
      return "undefined";
    }
  }, [videoTrack]);

  // const audioTrack = React.useMemo(
  //   () => tracks?.filter((track) => track.kind === Track.Kind.Audio)[0],
  //   [tracks]
  // );

  React.useEffect(() => {
    if (videoEl.current && videoTrack) {
      videoTrack.unmute();
      videoTrack.attach(videoEl.current);
    }

    return () => {
      videoTrack?.detach();
    };
  }, [videoTrack]);

  const handleValidation = React.useCallback(
    (values) => {
      if (typeof onValidate === "function") {
        return onValidate(values);
      } else {
        return values.username !== "";
      }
    },
    [onValidate]
  );

  React.useEffect(() => {
    const newUserChoices = {
      username,
      videoEnabled,
      videoDeviceId,
      audioEnabled,
      audioDeviceId,
    };
    setUserChoices(newUserChoices);
    setIsValid(handleValidation(newUserChoices));
  }, [
    username,
    videoEnabled,
    videoDeviceId,
    audioEnabled,
    audioDeviceId,
    handleValidation,
  ]);

  React.useEffect(() => {
    setAudioDeviceId(audioDeviceId);
    setDeviceIdAudio(audioDeviceId);
    setVideoDeviceId(videoDeviceId);
  }, [
    audioDeviceId,
    videoTrack,
    videoDeviceId,
    isVideoModalOpen
  ]);

  // Progressive permission checking using getUserMedia attempts
  const checkMediaPermissions = React.useCallback(async (mediaType = 'both') => {
    const results = {
      camera: 'unknown',
      microphone: 'unknown'
    };

    try {
      // For Safari/iOS or when permission API is not available, use getUserMedia directly
      if (isSafari || !hasPermissionAPI) {
        if (mediaType === 'camera' || mediaType === 'both') {
          try {
            const videoStream = await navigator.mediaDevices.getUserMedia({ video: true });
            videoStream.getTracks().forEach(track => track.stop());
            results.camera = 'granted';
          } catch (error) {
            results.camera = error.name === 'NotAllowedError' ? 'denied' : 'unknown';
          }
        }

        if (mediaType === 'microphone' || mediaType === 'both') {
          try {
            const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
            audioStream.getTracks().forEach(track => track.stop());
            results.microphone = 'granted';
          } catch (error) {
            results.microphone = error.name === 'NotAllowedError' ? 'denied' : 'unknown';
          }
        }
      } else {
        // Use permission API for Chrome/Firefox/Edge when available
        if (mediaType === 'camera' || mediaType === 'both') {
          try {
            const cameraPermission = await navigator.permissions.query({ name: 'camera' });
            // Map permission states: granted -> granted, denied -> denied, prompt -> unknown
            results.camera = cameraPermission.state === 'granted' ? 'granted' :
                            cameraPermission.state === 'denied' ? 'denied' : 'unknown';

            console.log('Camera permission state:', cameraPermission.state, '-> mapped to:', results.camera);
          } catch (error) {
            console.warn('Camera permission query failed:', error);
            results.camera = 'unknown';
          }
        }

        if (mediaType === 'microphone' || mediaType === 'both') {
          try {
            const micPermission = await navigator.permissions.query({ name: 'microphone' });
            // Map permission states: granted -> granted, denied -> denied, prompt -> unknown
            results.microphone = micPermission.state === 'granted' ? 'granted' :
                               micPermission.state === 'denied' ? 'denied' : 'unknown';

            console.log('Microphone permission state:', micPermission.state, '-> mapped to:', results.microphone);
          } catch (error) {
            console.warn('Microphone permission query failed:', error);
            results.microphone = 'unknown';
          }
        }
      }

      return results;
    } catch (error) {
      console.error('Permission check error:', error);
      return results;
    }
  }, [isSafari, hasPermissionAPI]);

  // Setup permission monitoring with real-time updates for supported browsers
  React.useEffect(() => {
    let permissionListeners = [];

    const setupPermissionMonitoring = async () => {
      try {
        // Initial permission check - only for browsers with permission API
        if (hasPermissionAPI && !isSafari) {
          const results = await checkMediaPermissions('both');
          setPermissions(results);

          // Setup real-time listeners for Chrome/Firefox/Edge
          try {
            const cameraPermission = await navigator.permissions.query({ name: 'camera' });
            const micPermission = await navigator.permissions.query({ name: 'microphone' });

            const handlePermissionChange = () => {
              checkMediaPermissions('both').then(setPermissions);
            };

            cameraPermission.addEventListener('change', handlePermissionChange);
            micPermission.addEventListener('change', handlePermissionChange);

            permissionListeners = [
              () => cameraPermission.removeEventListener('change', handlePermissionChange),
              () => micPermission.removeEventListener('change', handlePermissionChange)
            ];
          } catch (error) {
            console.warn('Failed to setup permission listeners:', error);
          }
        }
        // For Safari/iOS, permissions will be checked when user interacts with buttons
      } catch (error) {
        console.error('Error setting up permission monitoring:', error);
      }
    };

    setupPermissionMonitoring();

    return () => {
      permissionListeners.forEach(cleanup => cleanup());
    };
  }, [checkMediaPermissions, hasPermissionAPI, isSafari]);

  // Show permission modal logic - for desktop when permissions need to be requested
  React.useEffect(() => {
    console.log('Modal logic check:', {
      isMobile: isMobileBrowser(),
      isSafari,
      permissions,
      hasPermissionAPI
    });

    if (isMobileBrowser() || isSafari) {
      console.log('Skipping modal for mobile/Safari');
      return; // Don't show modal for mobile or Safari
    }

    // Show modal if permissions are not granted (either unknown, prompt, or denied)
    const cameraNotGranted = permissions.camera !== 'granted';
    const micNotGranted = permissions.microphone !== 'granted';
    const shouldShowModal = cameraNotGranted || micNotGranted;

    console.log('Modal decision:', {
      cameraNotGranted,
      micNotGranted,
      shouldShowModal
    });

    if (shouldShowModal) {
      if (cameraNotGranted && micNotGranted) {
        setPermissionType('both');
      } else if (cameraNotGranted) {
        setPermissionType('camera');
      } else if (micNotGranted) {
        setPermissionType('mic');
      }
      console.log('Showing permission modal');
      setShowPermissionModal(true);
    } else {
      console.log('Hiding permission modal');
      setShowPermissionModal(false);
    }
  }, [permissions.camera, permissions.microphone, isSafari]);

  // Request permissions using getUserMedia - works across all browsers
  const requestMediaPermissions = async (type) => {
    try {
      let constraints = {};

      if (type === 'camera') {
        constraints = { video: true };
      } else if (type === 'microphone') {
        constraints = { audio: true };
      } else if (type === 'both') {
        constraints = { video: true, audio: true };
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      stream.getTracks().forEach(track => track.stop());

      // Update permission state based on successful request
      const newPermissions = { ...permissions };
      if (type === 'camera' || type === 'both') {
        newPermissions.camera = 'granted';
      }
      if (type === 'microphone' || type === 'both') {
        newPermissions.microphone = 'granted';
      }
      setPermissions(newPermissions);

      // Refresh tracks if they were already enabled
      if ((type === 'camera' || type === 'both') && videoEnabled) {
        setVideoEnabled(false);
        setTimeout(() => setVideoEnabled(true), 100);
      }
      if ((type === 'microphone' || type === 'both') && audioEnabled) {
        setAudioEnabled(false);
        setTimeout(() => setAudioEnabled(true), 100);
      }

      return true;
    } catch (error) {
      console.error('Permission request error:', error);

      // Update permission state based on error type
      const newPermissions = { ...permissions };
      if (error.name === 'NotAllowedError') {
        if (type === 'camera' || type === 'both') {
          newPermissions.camera = 'denied';
        }
        if (type === 'microphone' || type === 'both') {
          newPermissions.microphone = 'denied';
        }
      }
      setPermissions(newPermissions);

      const deviceName = type === 'camera' ? 'Camera' :
                        type === 'microphone' ? 'Microphone' :
                        'Camera and microphone';
      setToastMessage(`${deviceName} access was denied. Please allow access in your browser settings.`);
      setToastStatus("error");
      setShowToast(true);

      return false;
    }
  };

  // Updated click handlers with progressive permission requests
  const handleAudioClick = async () => {
    // For mobile or Safari, request permission when user tries to enable
    if (permissions.microphone !== 'granted' && (isMobileBrowser() || isSafari)) {
      const success = await requestMediaPermissions('microphone');
      if (!success) return; // Don't enable if permission was denied
    }
    setAudioEnabled(!audioEnabled);
  };

  const handleVideoClick = async () => {
    // For mobile or Safari, request permission when user tries to enable
    if (permissions.camera !== 'granted' && (isMobileBrowser() || isSafari)) {
      const success = await requestMediaPermissions('camera');
      if (!success) return; // Don't enable if permission was denied
    }
    setVideoEnabled(!videoEnabled);
  };

  return (
    <div className="video-prejoin-container lk-prejoin" {...htmlProps}>
      <div
        className="mute-button"
        onClick={() => setAudioEnabled(!audioEnabled)}
      >
        {audioEnabled ? (
          <AudioOutlined className="control-icon" />
        ) : (
          <AudioMutedOutlined className="control-icon" />
        )}
      </div>
      <div className="lk-video-container">
        {videoTrack && videoEnabled ? (
          <video
            ref={videoEl}
            width="100%"
            height="100%"
            data-lk-facing-mode={trackFacingMode}
            className={isSelfVideoMirrored ? "mirrored-video" : "not-mirrored-video"}
            style={{
              objectFit: "cover",
              borderRadius: "15px",
              display: "block"
            }}
          >
            <track kind="captions" />
          </video>
        ) : (
          <div className="lk-camera-off-note">
            <div className="avatar-container">
              {avatarName || <UserOutlined className="avatar-icon" />}
            </div>
          </div>
        )}
      </div>
      <div
        className="lk-button-group-container control-bar-container"
      >
        {/* Audio Button with Dropdown */}
        <div className="lk-button-group audio" style={{ position: "relative" }}>
          <div
            className={`control-button ${permissions.microphone === 'denied' ? 'permission-warning' : ''}`}
            onClick={handleAudioClick}
          >
            {audioEnabled ? (
              <AudioOutlined className="control-icon" />
            ) : (
              <AudioMutedOutlined className="control-icon" />
            )}
            {permissions.microphone === 'denied' && (
              <WarningIcon className="warning-icon" />
            )}
          </div>

          {/* Mic Device Dropdown */}
          <MicDeviceDropdown
            deviceId={audioDeviceId}
            setDeviceId={setAudioDeviceId}
            track={tracks?.filter((track) => track.kind === Track.Kind.Audio)[0]}
            permissionsGranted={permissions.microphone === 'granted'}
          >
            <div className="dropdown-button">
              <CaretDownFilled className="dropdown-icon" />
            </div>
          </MicDeviceDropdown>
        </div>

        {/* Video Button with Dropdown */}
        <div className="lk-button-group video">
          <div
            className={`control-button ${permissions.camera === 'denied' ? 'permission-warning' : ''}`}
            onClick={handleVideoClick}
          >
            {videoEnabled ? (
              <VideoOn className="control-icon" />
            ) : (
              <VideoOff className="control-icon" />
            )}
            {permissions.camera === 'denied' && (
              <WarningIcon className="warning-icon" />
            )}
          </div>

          {/* Camera Device Dropdown */}
          <CameraDeviceDropdown
            deviceId={videoDeviceId}
            setDeviceId={setVideoDeviceId}
            permissionsGranted={permissions.camera === 'granted'}
          >
            <div className="dropdown-button">
              <CaretDownFilled className="dropdown-icon" />
            </div>
          </CameraDeviceDropdown>
        </div>

        {/* Virtual Background Button */}
        <div
          className="control-button"
          onClick={() => {
            setIsVisualEffectsModalOpen(true);
          }}
        >
          <VirtualBackgroundIcon className="control-icon virtual-bg-icon" />
        </div>

        {/* Settings Button */}
        <div
          className="control-button"
          onClick={() => {
            setIsVideoModalOpen(true);
          }}
        >
          <SettingOutlined className="control-icon" />
        </div>
      </div>

      {/* Audio Video Settings Modal */}
      {/* <AudioVideoSettingsModal
        audio={isAudioModalOpen}
        video={isVideoModalOpen}
        open={isAudioModalOpen || isVideoModalOpen}
        setOpen={isAudioModalOpen ? setIsAudioModalOpen : setIsVideoModalOpen}
        deviceId={isAudioModalOpen ? audioDeviceId : videoDeviceId}
        setDeviceId={isAudioModalOpen ? setAudioDeviceId : setVideoDeviceId}
        track={isAudioModalOpen ? audioTrack : videoTrack}
        setIsVisualEffectsModalOpen={setIsVisualEffectsModalOpen}
        isNoiseCancellationActive={isNoiseCancellationActive}
        setIsNoiseCancellationActive={setIsNoiseCancellationActive}
        isEchoCancellationActive={isEchoCancellationActive}
        setIsEchoCancellationActive={setIsEchoCancellationActive}
        isSelfVideoMirrored={isSelfVideoMirrored}
        setIsSelfVideoMirrored={setIsSelfVideoMirrored}
      /> */}

      {/* Audio Settings Modal */}
      {/* <Modal
        open={isAudioModalOpen}
        onCancel={() => setIsAudioModalOpen(false)}
        footer={null}
      >
        <AudioSettings
          deviceId={audioDeviceId}
          setDeviceId={setAudioDeviceId}
          track={audioTrack}
          speakerDeviceId={speakerDeviceId}
          setSpeakerDeviceId={setSpeakerDeviceId}
          room={room}
        />
      </Modal> */}

      {/* Video Settings Modal */}
      {/* <Modal
        open={isVideoModalOpen}
        onCancel={() => setIsVideoModalOpen(false)}
        footer={null}
      >
        <VideoSettings
          deviceId={videoDeviceId}
          setDeviceId={setVideoDeviceId}
          track={videoTrack}
          isSelfVideoMirrored={isSelfVideoMirrored}
          setIsSelfVideoMirrored={setIsSelfVideoMirrored}
          setIsVisualEffectsModalOpen={setIsVisualEffectsModalOpen}
          open={isVideoModalOpen}
          trackFacingMode={trackFacingMode}
        />
      </Modal> */}

      {/* Virtual Background Modal */}
      <VirtualBackgroundModal
        open={isVisualEffectsModalOpen}
        setOpen={setIsVisualEffectsModalOpen}
        backgrounds={backgrounds}
        setBackgrounds={setBackgrounds}
        room={room}
      />

      {/* Permission Modal - Only show for desktop when permissions are explicitly denied */}
      {!isMobileBrowser() && (
        <PermissionUi
          open={showPermissionModal}
          onClose={() => setShowPermissionModal(false)}
          permissionType={permissionType}
          onAllow={async (action) => {
            let success = false;

            if (action === 'camera') {
              success = await requestMediaPermissions('camera');
            } else if (action === 'mic') {
              success = await requestMediaPermissions('microphone');
            } else if (action === 'mic_camera') {
              success = await requestMediaPermissions('both');
            } else if (action === 'skip') {
              setShowPermissionModal(false);
              return;
            }

            // Close modal if permissions were granted successfully
            if (success) {
              setShowPermissionModal(false);
            }
          }}
        />
      )}
    </div>
  );
}